// ignore: depend_on_referenced_packages
import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/move_out_model/move_out_model.dart';
import 'package:room_eight/models/move_in_model/move_in_model.dart';
import 'package:room_eight/repository/like_repository/like_repository.dart';

part 'like_event.dart';
part 'like_state.dart';

class LikeBloc extends Bloc<LikeEvent, LikeState> {
  final PageController pageController = PageController();
  final LikeRepository likeRepository;
  LikeBloc(this.likeRepository) : super(LikeState()) {
    on<TabChangedEvent>(_onTabChangedEvent);
    on<LoadMoveOutData>(_onLoadMoveOutData);
    on<LoadMoveInData>(_onLoadMoveInData);
    on<AcceptLikeEvent>(_onAcceptLikeEvent);
    on<ResetNavigationEvent>(_onResetNavigationEvent);
  }
  void _onTabChangedEvent(TabChangedEvent event, Emitter<LikeState> emit) {
    pageController.jumpToPage(event.newIndex);
    emit(state.copyWith(curentTebIndex: event.newIndex));
  }

  void _onLoadMoveOutData(
    LoadMoveOutData event,
    Emitter<LikeState> emit,
  ) async {
    emit(state.copyWith(isLoadingMoveOut: true));
    try {
      MoveOutModel moveOut = await likeRepository.getMoveOutData();

      if (moveOut.status == true) {
        final List<MoveOutUser> moveOutData = moveOut.data ?? [];

        emit(state.copyWith(moveOutData: moveOutData, isLoadingMoveOut: false));
      } else {
        emit(state.copyWith(isLoadingMoveOut: false));
      }
    } catch (e) {
      emit(state.copyWith(isLoadingMoveOut: false));
    }
  }

  void _onLoadMoveInData(LoadMoveInData event, Emitter<LikeState> emit) async {
    emit(state.copyWith(isLoadingMoveIn: true));
    try {
      MoveInModel moveIn = await likeRepository.getMoveInData();

      if (moveIn.status == true) {
        final List<MoveInUser> moveInData = moveIn.data ?? [];

        emit(state.copyWith(moveInData: moveInData, isLoadingMoveIn: false));
      } else {
        emit(state.copyWith(isLoadingMoveIn: false));
      }
    } catch (e) {
      emit(state.copyWith(isLoadingMoveIn: false));
    }
  }

  void _onAcceptLikeEvent(
    AcceptLikeEvent event,
    Emitter<LikeState> emit,
  ) async {
    emit(state.copyWith(isAcceptingLike: true));
    try {
      final result = await likeRepository.acceptLike(
        likeProfileId: event.profileId,
        isAccept: event.isAccept,
      );

      if (result.status == true) {
        // Remove the user from moveInData after successful accept/reject
        final updatedMoveInData = state.moveInData
            .where((user) => user.id != event.profileId)
            .toList();

        emit(
          state.copyWith(
            moveInData: updatedMoveInData,
            isAcceptingLike: false,
            shouldNavigateToChat: event.isAccept && event.user != null,
            acceptedUser: event.isAccept ? event.user : null,
            toMessageId: event.isAccept ? (event.user?.toMessageId) : null,
          ),
        );
        NavigatorService.pushNamed(AppRoutes.chatscreen, arguments: event.user );
      } else {
        emit(state.copyWith(isAcceptingLike: false));
      }
    } catch (e) {
      emit(state.copyWith(isAcceptingLike: false));
    }
  }

  void _onResetNavigationEvent(
    ResetNavigationEvent event,
    Emitter<LikeState> emit,
  ) {
    emit(state.resetNavigation());
  }

  @override
  Future<void> close() {
    pageController.dispose();
    return super.close();
  }
}
